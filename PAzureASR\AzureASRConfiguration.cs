﻿using System;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class AzureASRConfiguration
    {
        public string AzureAuthorizationEndpointHost
        {
            get;
            set;
        }

        public int AzureAuthorizationEndpointPort
        {
            get;
            set;
        }

        public string AzureManagementEndpointHost
        {
            get;
            set;
        }

        public int AzureManagementEndpointPort
        {
            get;
            set;
        }

        public string AzureRSVaultName
        {
            get;
            set;
        }

        public string AzureRSVaultRegion
        {
            get;
            set;
        }

        public string AzureRSVaultResourceGroupName
        {
            get;
            set;
        }

        public string AzureRSVMNameSource
        {
            get;
            set;
        }

        public string AzureRSVMNameTarget
        {
            get;
            set;
        }

        public string AzureRSVMRegionSource
        {
            get;
            set;
        }

        public string AzureRSVMRegionTarget
        {
            get;
            set;
        }

        public string AzureRSVMResourceGroupNameSource
        {
            get;
            set;
        }

        public string AzureRSVMResourceGroupNameTarget
        {
            get;
            set;
        }

        public string AzureServicePrincipalClientID
        {
            get;
            set;
        }

        public string AzureServicePrincipalSecretID
        {
            get;
            set;
        }

        public string AzureSubscriptionID
        {
            get;
            set;
        }

        public string AzureTenantID
        {
            get;
            set;
        }

        public string AzureUserName
        {
            get;
            set;
        }

        public string AzureUserPassword
        {
            get;
            set;
        }

        public bool IsADAuthentication
        {
            get;
            set;
        }

        public bool IsProxyServerEnabled
        {
            get;
            set;
        }

        public bool IsProxyWithAuthentication
        {
            get;
            set;
        }

        public bool IsSPNAuthentication
        {
            get;
            set;
        }

        public string ProxyIPAddress
        {
            get;
            set;
        }

        public string ProxyPassword
        {
            get;
            set;
        }

        public int ProxyPort
        {
            get;
            set;
        }

        public string ProxyUsername
        {
            get;
            set;
        }

        public AzureASRConfiguration()
        {
        }
    }
}