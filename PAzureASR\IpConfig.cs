﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class IpConfig
    {
        public string ipAddressType
        {
            get;
            set;
        }

        public bool isPrimary
        {
            get;
            set;
        }

        public bool isSeletedForFailover
        {
            get;
            set;
        }

        public object name
        {
            get;
            set;
        }

        public string recoveryIPAddressType
        {
            get;
            set;
        }

        public List<object> recoveryLBBackendAddressPoolIds
        {
            get;
            set;
        }

        public string recoveryPublicIPAddressId
        {
            get;
            set;
        }

        public string recoveryStaticIPAddress
        {
            get;
            set;
        }

        public string recoverySubnetName
        {
            get;
            set;
        }

        public object staticIPAddress
        {
            get;
            set;
        }

        public object subnetName
        {
            get;
            set;
        }

        public List<object> tfoLBBackendAddressPoolIds
        {
            get;
            set;
        }

        public string tfoPublicIPAddressId
        {
            get;
            set;
        }

        public string tfoStaticIPAddress
        {
            get;
            set;
        }

        public string tfoSubnetName
        {
            get;
            set;
        }

        public IpConfig()
        {
        }
    }
}