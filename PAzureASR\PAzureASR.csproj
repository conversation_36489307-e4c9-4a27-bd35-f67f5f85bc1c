﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2652ECF3-EAA7-42BD-A909-66FAD4D598AD}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>PAzureASR</RootNamespace>
    <AssemblyName>PAzureASR</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net">
      <HintPath>bin\Release\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.JScript" />
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Release\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AzureASRConfiguration.cs" />
    <Compile Include="AzureSiteRecovery.cs" />
    <Compile Include="CurrentScenario.cs" />
    <Compile Include="IpConfig.cs" />
    <Compile Include="Properties.cs" />
    <Compile Include="Properties1.cs" />
    <Compile Include="Properties2.cs" />
    <Compile Include="Properties3.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ProtectedManagedDisk.cs" />
    <Compile Include="ProviderSpecificDetails.cs" />
    <Compile Include="ProviderSpecificDetails3.cs" />
    <Compile Include="proxyserver.cs" />
    <Compile Include="Root.cs" />
    <Compile Include="Root1.cs" />
    <Compile Include="Root2.cs" />
    <Compile Include="Root3.cs" />
    <Compile Include="Value.cs" />
    <Compile Include="Value1.cs" />
    <Compile Include="Value2.cs" />
    <Compile Include="Value3.cs" />
    <Compile Include="VMMonitoring.cs" />
    <Compile Include="VmNic.cs" />
    <Compile Include="VmNic3.cs" />
    <Compile Include="VMReplication.cs" />
    <Compile Include="VmSyncedConfigDetails.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>