﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class Properties
    {
        public string activeLocation
        {
            get;
            set;
        }

        public List<string> allowedOperations
        {
            get;
            set;
        }

        public CurrentScenario currentScenario
        {
            get;
            set;
        }

        public string friendlyName
        {
            get;
            set;
        }

        public string ipAddress
        {
            get;
            set;
        }

        public DateTime? lastSuccessfulTestFailoverTime
        {
            get;
            set;
        }

        public string policyId
        {
            get;
            set;
        }

        public string primaryFabricFriendlyName
        {
            get;
            set;
        }

        public string primaryFabricProvider
        {
            get;
            set;
        }

        public string primaryProtectionContainerFriendlyName
        {
            get;
            set;
        }

        public object protectableItemId
        {
            get;
            set;
        }

        public string protectedItemType
        {
            get;
            set;
        }

        public string protectionState
        {
            get;
            set;
        }

        public string protectionStateDescription
        {
            get;
            set;
        }

        public ProviderSpecificDetails providerSpecificDetails
        {
            get;
            set;
        }

        public string recoveryFabricFriendlyName
        {
            get;
            set;
        }

        public string recoveryFabricId
        {
            get;
            set;
        }

        public DateTime? recoveryPointTime
        {
            get;
            set;
        }

        public string recoveryPointType
        {
            get;
            set;
        }

        public string recoveryProtectionContainerFriendlyName
        {
            get;
            set;
        }

        public string recoveryServicesProviderId
        {
            get;
            set;
        }

        public string replicationHealth
        {
            get;
            set;
        }

        public string testFailoverState
        {
            get;
            set;
        }

        public Properties()
        {
        }
    }
}