﻿using System;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class ProtectedManagedDisk
    {
        public object allowedDiskLevelOperation
        {
            get;
            set;
        }

        public double dataPendingAtSourceAgentInMB
        {
            get;
            set;
        }

        public double dataPendingInStagingStorageAccountInMB
        {
            get;
            set;
        }

        public object dekKeyVaultArmId
        {
            get;
            set;
        }

        public object diskCapacityInBytes
        {
            get;
            set;
        }

        public string diskId
        {
            get;
            set;
        }

        public string diskName
        {
            get;
            set;
        }

        public object diskState
        {
            get;
            set;
        }

        public string diskType
        {
            get;
            set;
        }

        public string failoverDiskName
        {
            get;
            set;
        }

        public bool isDiskEncrypted
        {
            get;
            set;
        }

        public bool isDiskKeyEncrypted
        {
            get;
            set;
        }

        public object kekKeyVaultArmId
        {
            get;
            set;
        }

        public object keyIdentifier
        {
            get;
            set;
        }

        public object monitoringJobType
        {
            get;
            set;
        }

        public object monitoringPercentageCompletion
        {
            get;
            set;
        }

        public object primaryDiskEncryptionSetId
        {
            get;
            set;
        }

        public string primaryStagingAzureStorageAccountId
        {
            get;
            set;
        }

        public object recoveryDiskEncryptionSetId
        {
            get;
            set;
        }

        public string recoveryOrignalTargetDiskId
        {
            get;
            set;
        }

        public string recoveryReplicaDiskAccountType
        {
            get;
            set;
        }

        public string recoveryReplicaDiskId
        {
            get;
            set;
        }

        public string recoveryResourceGroupId
        {
            get;
            set;
        }

        public string recoveryTargetDiskAccountType
        {
            get;
            set;
        }

        public string recoveryTargetDiskId
        {
            get;
            set;
        }

        public bool resyncRequired
        {
            get;
            set;
        }

        public object secretIdentifier
        {
            get;
            set;
        }

        public string tfoDiskName
        {
            get;
            set;
        }

        public ProtectedManagedDisk()
        {
        }
    }
}