﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class ProviderSpecificDetails
    {
        public string autoProtectionOfDataDisk
        {
            get;
            set;
        }

        public string fabricObjectId
        {
            get;
            set;
        }

        public object initialPrimaryExtendedLocation
        {
            get;
            set;
        }

        public string initialPrimaryFabricLocation
        {
            get;
            set;
        }

        public string initialPrimaryZone
        {
            get;
            set;
        }

        public object initialRecoveryExtendedLocation
        {
            get;
            set;
        }

        public string initialRecoveryFabricLocation
        {
            get;
            set;
        }

        public string initialRecoveryZone
        {
            get;
            set;
        }

        public string instanceType
        {
            get;
            set;
        }

        public bool isReplicationAgentCertificateUpdateRequired
        {
            get;
            set;
        }

        public bool isReplicationAgentUpdateRequired
        {
            get;
            set;
        }

        public DateTime? lastRpoCalculatedTime
        {
            get;
            set;
        }

        public string lifecycleId
        {
            get;
            set;
        }

        public string managementId
        {
            get;
            set;
        }

        public object monitoringJobType
        {
            get;
            set;
        }

        public object monitoringPercentageCompletion
        {
            get;
            set;
        }

        public string multiVmGroupCreateOption
        {
            get;
            set;
        }

        public string multiVmGroupId
        {
            get;
            set;
        }

        public string multiVmGroupName
        {
            get;
            set;
        }

        public string osType
        {
            get;
            set;
        }

        public object primaryAvailabilityZone
        {
            get;
            set;
        }

        public object primaryExtendedLocation
        {
            get;
            set;
        }

        public string primaryFabricLocation
        {
            get;
            set;
        }

        public object protectedDisks
        {
            get;
            set;
        }

        public List<ProtectedManagedDisk> protectedManagedDisks
        {
            get;
            set;
        }

        public object recoveryAvailabilitySet
        {
            get;
            set;
        }

        public object recoveryAvailabilityZone
        {
            get;
            set;
        }

        public string recoveryAzureGeneration
        {
            get;
            set;
        }

        public string recoveryAzureResourceGroupId
        {
            get;
            set;
        }

        public string recoveryAzureVMName
        {
            get;
            set;
        }

        public string recoveryAzureVMSize
        {
            get;
            set;
        }

        public object recoveryBootDiagStorageAccountId
        {
            get;
            set;
        }

        public object recoveryCapacityReservationGroupId
        {
            get;
            set;
        }

        public object recoveryCloudService
        {
            get;
            set;
        }

        public object recoveryExtendedLocation
        {
            get;
            set;
        }

        public string recoveryFabricLocation
        {
            get;
            set;
        }

        public object recoveryFabricObjectId
        {
            get;
            set;
        }

        public object recoveryProximityPlacementGroupId
        {
            get;
            set;
        }

        public object recoveryVirtualMachineScaleSetId
        {
            get;
            set;
        }

        public int? rpoInSeconds
        {
            get;
            set;
        }

        public object rtoInSeconds
        {
            get;
            set;
        }

        public string selectedRecoveryAzureNetworkId
        {
            get;
            set;
        }

        public object selectedTfoAzureNetworkId
        {
            get;
            set;
        }

        public object testFailoverRecoveryFabricObjectId
        {
            get;
            set;
        }

        public string tfoAzureVMName
        {
            get;
            set;
        }

        public object unprotectedDisks
        {
            get;
            set;
        }

        public string vmEncryptionType
        {
            get;
            set;
        }

        public List<VmNic> vmNics
        {
            get;
            set;
        }

        public string vmProtectionState
        {
            get;
            set;
        }

        public string vmProtectionStateDescription
        {
            get;
            set;
        }

        public VmSyncedConfigDetails vmSyncedConfigDetails
        {
            get;
            set;
        }

        public ProviderSpecificDetails()
        {
        }
    }
}