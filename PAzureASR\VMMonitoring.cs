﻿using System;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class VMMonitoring
    {
        public string ProtectedActiveLocationState
        {
            get;
            set;
        }

        public string ProtectedAllowedOperations
        {
            get;
            set;
        }

        public string ProtectedLocationName
        {
            get;
            set;
        }

        public string ProtectedPublicIPAddress
        {
            get;
            set;
        }

        public string ProtectedReplicationProvider
        {
            get;
            set;
        }

        public string ProtectedServicesVaultName
        {
            get;
            set;
        }

        public string ProtectedVirtualMachineName
        {
            get;
            set;
        }

        public string RecoveryActiveLocationState
        {
            get;
            set;
        }

        public string RecoveryAllowedOperations
        {
            get;
            set;
        }

        public string RecoveryLocationName
        {
            get;
            set;
        }

        public string RecoveryPublicIPAddress
        {
            get;
            set;
        }

        public string RecoveryReplicationProvider
        {
            get;
            set;
        }

        public string RecoveryServicesVaultName
        {
            get;
            set;
        }

        public string RecoveryVirtualMachineName
        {
            get;
            set;
        }

        public VMReplication Vmreplication
        {
            get;
            set;
        }

        public VMMonitoring()
        {
        }
    }
}