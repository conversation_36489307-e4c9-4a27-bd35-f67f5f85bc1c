﻿using System;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class VMReplication
    {
        public string ProtectedAppConsistent
        {
            get;
            set;
        }

        public string ProtectedCrashConsistent
        {
            get;
            set;
        }

        public string ProtectedLastSuccessfulFailoverTime
        {
            get;
            set;
        }

        public string ProtectedLastSuccessfulTestFailoverTime
        {
            get;
            set;
        }

        public string ProtectedReplicationHealth
        {
            get;
            set;
        }

        public string ProtectedRPO
        {
            get;
            set;
        }

        public string ProtectedStatus
        {
            get;
            set;
        }

        public string RecoveryAppConsistent
        {
            get;
            set;
        }

        public string RecoveryCrashConsistent
        {
            get;
            set;
        }

        public string RecoveryLastSuccessfulFailoverTime
        {
            get;
            set;
        }

        public string RecoveryLastSuccessfulTestFailoverTime
        {
            get;
            set;
        }

        public string RecoveryReplicationHealth
        {
            get;
            set;
        }

        public string RecoveryRPO
        {
            get;
            set;
        }

        public string RecoveryStatus
        {
            get;
            set;
        }

        public VMReplication()
        {
        }
    }
}