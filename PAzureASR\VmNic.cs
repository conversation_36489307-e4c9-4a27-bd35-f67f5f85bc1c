﻿using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class VmNic
    {
        public bool enableAcceleratedNetworkingOnRecovery
        {
            get;
            set;
        }

        public object enableAcceleratedNetworkingOnTfo
        {
            get;
            set;
        }

        public List<IpConfig> ipConfigs
        {
            get;
            set;
        }

        public string nicId
        {
            get;
            set;
        }

        public string recoveryNetworkSecurityGroupId
        {
            get;
            set;
        }

        public object recoveryNicName
        {
            get;
            set;
        }

        public object recoveryNicResourceGroupName
        {
            get;
            set;
        }

        public string recoveryVMNetworkId
        {
            get;
            set;
        }

        public object replicaNicId
        {
            get;
            set;
        }

        public bool reuseExistingNic
        {
            get;
            set;
        }

        public string selectionType
        {
            get;
            set;
        }

        public string sourceNicArmId
        {
            get;
            set;
        }

        public object targetNicName
        {
            get;
            set;
        }

        public object tfoNetworkSecurityGroupId
        {
            get;
            set;
        }

        public object tfoRecoveryNicName
        {
            get;
            set;
        }

        public object tfoRecoveryNicResourceGroupName
        {
            get;
            set;
        }

        public bool tfoReuseExistingNic
        {
            get;
            set;
        }

        public object tfoVMNetworkId
        {
            get;
            set;
        }

        public string vMNetworkName
        {
            get;
            set;
        }

        public VmNic()
        {
        }
    }
}