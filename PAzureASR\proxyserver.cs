﻿using System;
using System.Runtime.CompilerServices;

namespace PAzureASR
{
    public class proxyserver
    {
        public string IpAddress
        {
            get;
            set;
        }

        public string Password
        {
            get;
            set;
        }

        public int Port
        {
            get;
            set;
        }

        public string Username
        {
            get;
            set;
        }

        public proxyserver()
        {
        }

        public proxyserver(string ipAddress, int port, string username = null, string password = null)
        {
            this.IpAddress = ipAddress;
            this.Port = port;
            this.Username = username;
            this.Password = password;
        }
    }
}